/** @type {import('tailwindcss').Config} */
export default {
  darkMode: ['class'],
  content: [
    './pages/**/*.{ts,tsx}',
    './components/**/*.{ts,tsx}',
    './app/**/*.{ts,tsx}',
    './src/**/*.{ts,tsx,css}',
    './index.html',
  ],
  prefix: '',
  theme: {
    container: {
      center: true,
      padding: '2rem',
      screens: {
        '2xl': '1400px',
        md: '768px',
      },
    },
    extend: {
      fontSize: {
        xs: ['0.75rem', '1.25rem'],
        sm: ['0.875rem', '1.5rem'],
        base: ['1rem', '1.5rem'],
        lg: ['1.5rem', '2rem'],
        xl: ['2rem', '2.5rem'],
        '2xl': ['2.5rem', '3rem'],
      },
      colors: {
        primary: 'rgb(var(--primary-raw) / <alpha-value>)',
        error: 'rgb(var(--error-raw) / <alpha-value>)',
        success: 'rgb(var(--success-raw) / <alpha-value>)',
        'liner-blue': 'rgb(var(--liner-blue-raw) / <alpha-value>)',
        'liner-pink': 'rgb(var(--liner-pink-raw) / <alpha-value>)',
        'neutral-50': 'rgb(var(--neutral-50-raw) / <alpha-value>)',
        'neutral-100': 'rgb(var(--neutral-100-raw) / <alpha-value>)', // d1
        'neutral-200': 'rgb(var(--neutral-200-raw) / <alpha-value>)', // d2
        'neutral-300': 'rgb(var(--neutral-300-raw) / <alpha-value>)', // d3
        'neutral-400': 'rgb(var(--neutral-400-raw) / <alpha-value>)', // d4
        'neutral-500': 'rgb(var(--neutral-500-raw) / <alpha-value>)', // d5
        'neutral-600': 'rgb(var(--neutral-600-raw) / <alpha-value>)', // light-1
        'neutral-700': 'rgb(var(--neutral-700-raw) / <alpha-value>)', // light-2
        'neutral-800': 'rgb(var(--neutral-800-raw) / <alpha-value>)', // light-3
        'neutral-900': 'rgb(var(--neutral-900-raw) / <alpha-value>)',

        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        shadPrimary: {
          DEFAULT: 'hsl(var(--shad-primary))',
          foreground: 'hsl(var(--shad-primary-foreground))',
        },
        shadSecondary: {
          DEFAULT: 'hsl(var(--shad-secondary))',
          foreground: 'hsl(var(--shad-secondary-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
      },
      borderRadius: {
        lg: 'calc(var(--radius) + 8px)',
        md: 'var(--radius)',
        sm: 'calc(var(--radius) - 4px)',
      },
      borderWidth: {
        3: '3px',
      },
      borderColor: {
        'light-primary': 'rgba(139, 92, 246, 0.16)',
      },
      keyframes: {
        'opacity-in': {
          from: { opacity: 0 },
          to: { opacity: 1 },
        },
        'opacity-out': {
          from: { opacity: 1 },
          to: { opacity: 0 },
        },
        'text-fade-in': {
          '0%': {
            transform: 'scale(0.5)',
            opacity: 0,
          },
          '40%': {
            transform: 'scale(1.32)',
            opacity: 0.8,
          },
          '100%': {
            transform: 'scale(1)',
            opacity: 1,
          },
        },
        'float-in': {
          '0%': {
            transform: 'translateY(0px)',
          },
          '50%': {
            transform: 'translateY(-6px)',
          },
          '100%': {
            transform: 'translateY(0px)',
          },
        },
        'button-float': {
          '0%': {
            transform: 'translateY(0px)',
          },
          '50%': {
            transform: 'translateY(-20px)',
          },
          '100%': {
            transform: 'translateY(0px)',
          },
        },
        'breathing-glow': {
          '0%': {
            boxShadow:
              '0 0 20px rgba(214, 92, 225, 0.3), 0 0 40px rgba(128, 114, 237, 0.2), 0 0 60px rgba(214, 92, 225, 0.1)',
          },
          '50%': {
            boxShadow:
              '0 0 80px rgba(214, 92, 225, 0.8), 0 0 120px rgba(128, 114, 237, 0.6), 0 0 200px rgba(214, 92, 225, 0.6)',
          },
          '100%': {
            boxShadow:
              '0 0 20px rgba(214, 92, 225, 0.3), 0 0 40px rgba(128, 114, 237, 0.2), 0 0 60px rgba(214, 92, 225, 0.1)',
          },
        },
      },
      animation: {
        'opacity-in': 'opacity-in 0.2s ',
        'opacity-out': 'opacity-out 0.2s ',
        'text-fade-in': 'text-fade-in 0.36s ease-in-out 3s forwards',
        'float-in': 'float-in 3s ease-in-out infinite',
        'button-float': 'button-float 6s ease-in-out infinite',
        'breathing-glow': 'breathing-glow 6s ease-in-out infinite',
      },
      screens: {
        ipad: { min: '768px', max: '1024px' }, // 适配的是竖屏模式下的ipad，不考虑横屏的尺寸
        phone: { min: '320px', max: '475px' }, // 适配的是竖屏模式下的iphone，不考虑横屏的尺寸
        iphone: { min: '320px', max: '475px' }, // 适配的是竖屏模式下的iphone，不考虑横屏的尺寸
      },
    },
  },
  plugins: [require('tailwindcss-animate')],
}
